<script setup>
import { defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import NavBar from "./views/NavBar.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location"]);
const selectPreference = (preference) => {
  props.moveNextSlide(preference);
};

// Construct CDN URLs for the side image
const cdnBase = config.cdn;
const imgPath = config.images.img;
const sideImageUrl = computed(() => `${cdnBase}${imgPath}116.webp`);
</script>

<template>
  <div class="warning-page">
    <!-- Navigation Bar -->
    <NavBar />

    <!-- Gradient Background -->
    <div class="gradient-background">
      <!-- Main Content Container -->
      <div class="main-content">
        <!-- Warning Dialog -->
        <div class="warning-dialog">
          <div class="warning-content">
            <h2 class="warning-title">WARNING!</h2>
            <p class="warning-text">Before we can show you a list and photos of women who live near you and are ready to have sex right now, we need to ask a few quick questions.</p>
            <button class="continue-btn" @click="selectPreference('continue')">CONTINUE</button>
          </div>
        </div>

        <!-- Side Image -->
        <div class="side-image-circle">
          <img :src="sideImageUrl" alt="Profile Image" class="circle-image" />
        </div>
      </div>

      <!-- Adult Only Text -->
      <div class="adult-only-text">ADULT ONLY</div>
    </div>

    <Steps :step="steps" />
  </div>
</template>

<style>
/* Warning Page Styles */
.warning-page {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
}

.gradient-background {
  min-height: calc(100vh - 60px); /* Account for navbar height */
  background: linear-gradient(135deg, #a855f7 0%, #06b6d4 25%, #ec4899 50%, #8b5cf6 75%, #06b6d4 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 2rem 4rem;
}

.main-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.warning-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2.5rem;
  max-width: 450px;
  width: 100%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
  flex-shrink: 0;
  margin-left: 2rem;
}

.warning-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.warning-text {
  font-size: 1.1rem;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  text-align: left;
}

.continue-btn {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.continue-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(16, 185, 129, 0.4);
  background: linear-gradient(135deg, #059669, #047857);
}

.side-image-circle {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  border: 8px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  animation: float 3s ease-in-out infinite;
  flex-shrink: 0;
  margin-right: 2rem;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(-50%);
  }
  50% {
    transform: translateY(-60%);
  }
}

.circle-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.adult-only-text {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  font-weight: bold;
  letter-spacing: 2px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .gradient-background {
    padding: 1rem;
    justify-content: center;
    min-height: calc(100vh - 80px); /* Account for navbar on mobile */
  }

  .main-content {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
    justify-content: center;
  }

  .warning-dialog {
    margin-left: 0;
    margin-bottom: 0;
    padding: 2rem 1.5rem;
    max-width: 90%;
  }

  .warning-title {
    font-size: 2rem;
  }

  .warning-text {
    font-size: 1rem;
    text-align: center;
  }

  .continue-btn {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .side-image-circle {
    width: 200px;
    height: 200px;
    margin-right: 0;
  }

  .adult-only-text {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  .gradient-background {
    padding: 0.5rem;
  }

  .warning-dialog {
    padding: 1.5rem 1rem;
    margin-left: 0;
  }

  .warning-title {
    font-size: 1.8rem;
  }

  .warning-text {
    font-size: 0.9rem;
  }

  .side-image-circle {
    width: 150px;
    height: 150px;
    margin-right: 0;
  }
}
</style>
