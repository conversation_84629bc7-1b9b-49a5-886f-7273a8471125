<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../sites/lp8/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const validEmail = ref(false);

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  validEmail.value = emailPattern.test(props.inputs.email.value);
};

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailFromParams = urlParams.get("email");

  if (emailFromParams) {
    props.inputs.email.value = emailFromParams;
    setTimeout(() => {
      const emailInput = document.getElementById("email");
      if (emailInput) {
        emailInput.value = emailFromParams;
      }
    }, 0);
  }
});

const continueToNextSlide = () => {
  validateEmail();
  const alertMessage = document.getElementById("alert-message").innerText;

  if (!validEmail.value) {
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
  } else {
    props.moveNextSlide();
  }
};

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};
// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  // Use images from 120.webp to 150.webp (31 images total)
  const imageNumber = 120 + ((number - 1) % 31);
  return `${cdnBase}${imgPath}${imageNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}30.webp`);

// Add countdown functionality
const minutes = ref(1);
const seconds = ref(39);

const formattedTime = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 59;
    } else {
      clearInterval(timer);
    }
  }, 1000);
});
</script>

<template>
  <div class="six">
    <!-- Profile Gallery -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-wrapper">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 95%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Left Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Email Form Content -->
        <div class="col-md-5 d-flex justify-content-center align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime }} to register!</p>
              </div>
            </div>
            <div class="clip">
              <h4 class="text-center fw-bold fs-6 text-lighth shadow-8d">{{ Language.local_area }}</h4>
              <h3 class="text-center text-light8 shadow-8 mt-3">{{ Language.email_text }}</h3>

              <div class="form-section mt-4">
                <div class="input-group">
                  <input v-model="props.inputs.email.value" class="form-control custom-input" id="email" placeholder="e.g. <EMAIL>" type="email" @keypress="handleKeyPress" />
                  <button type="button" class="btn next-btn" @click="continueToNextSlide('yes')">
                    {{ Language.continue }}
                  </button>
                </div>
                <div class="text-center mt-3">
                  <p class="spam-alert">{{ Language.spam_alert }}</p>
                </div>
                <Searching :location="location" :language="Language" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="alert-message" style="display: none">
      {{ Language.email_error }}
    </div>

    <Steps :step="steps" />
  </div>
</template>

<style scoped>
/* Include styles from PageOne plus: */

.form-section {
  max-width: 400px;
  margin: 0 auto;
}

.input-group {
  display: flex;
  margin: 0 auto;
  width: 100%;
}
.shadow-8d {
  font-size: 15px !important;
}

.custom-input {
  border: 1px solid #dee2e6;
  padding: 0.75rem;
  border-radius: 4px 0 0 4px !important;
  transition: all 0.3s ease;
  width: 70% !important;
}

.custom-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.next-btn {
  background-color: #116d6e !important;
  color: #f6eca9 !important;
  font-size: 14px !important;
  border-radius: 0 4px 4px 0 !important;
  transition: all 0.3s ease;
  width: 30%;
}

.next-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.spam-alert {
  color: #666;
  font-size: 10px;
}

.text-lighth {
  color: #282d4f !important;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.text-lighti {
  color: #a0204c !important;
  font-size: 16px !important;
  text-shadow: 3px 7px 8px rgba(134, 126, 126, 0.5);
}

.text-light8 {
  color: #282d4f !important;
  font-size: 24px !important;
}

.shadow-8 {
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
  font-size: 20px !important;
}

/* Keep the rest of the styles from PageOne */
</style>
