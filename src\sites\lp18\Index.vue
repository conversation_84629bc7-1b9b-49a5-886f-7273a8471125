<script setup>
import { ref, onMounted, provide, watch } from "vue";
import Axios from "axios";
import PageOne from "../lp18/components/PageOne.vue";
import PageTwo from "../lp18/components/PageTwo.vue";
import PageThree from "../lp18/components/PageThree.vue";
import PageFour from "../lp18/components/PageFour.vue";
import PageFive from "../lp18/components/PageFive.vue";
import PageSix from "../lp18/components/PageSix.vue";
import PageSeven from "../lp18/components/PageSeven.vue";
import Navbar from "../../sites/lp18/components/views/NavBar.vue";
import Footer from "../../sites/lp18/components/views/Footer.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "./../../assets/js/config/config.json";
import Background from "../../sites/lp15/components/views/Background.vue";

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);

provide("assets", config.assets);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }
  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    inputData.lander.value = Params().path;
    inputData.click_id.value = Params().click_id;
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    inputData.locale.value = Params().locale;
    inputData.media.value = Media().device;
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.image.value = Params().image;
    inputData.traf_id.value = Params().traf_id;
    inputData.tsid.value = Params().tsid;
    inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });

  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);

  function handlePopstate() {
    window.location.href = config.links.back_button + Params().url;
  }

  // Create floating bubbles
  createBubbles();
});

function createBubbles() {
  const background = document.querySelector(".animated-background");
  if (!background) return;

  for (let i = 0; i < 15; i++) {
    const bubble = document.createElement("div");
    bubble.classList.add("bubble");

    // Random size
    const size = Math.random() * 60 + 20;
    bubble.style.width = `${size}px`;
    bubble.style.height = `${size}px`;

    // Random position
    bubble.style.left = `${Math.random() * 100}%`;

    // Random delay
    const delay = Math.random() * 15;
    bubble.style.animationDelay = `${delay}s`;

    // Random duration
    const duration = Math.random() * 10 + 10;
    bubble.style.animationDuration = `${duration}s`;

    background.appendChild(bubble);
  }
}

const moveNextSlide = () => {
  if (hasEmailParam.value && steps.value === 5) {
    steps.value = 7;
  } else {
    steps.value++;
  }
};

const moveBackSlide = () => {
  if (hasEmailParam.value && steps.value === 7) {
    steps.value = 5;
  } else {
    steps.value--;
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});
</script>

<template>
  <div class="animated-background">
    <div class="hearts"></div>
  </div>
  <Background />
  <div class="layoutlp15">
    <Navbar />
    <div class="content-wrapper">
      <div class="d-flex justify-content-center lp15x">
        <div class="bg-transparent">
          <div v-if="steps === 1">
            <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 2">
            <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 3">
            <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 4">
            <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 5">
            <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 6 && !hasEmailParam">
            <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
          <div v-else-if="steps === 7 || (steps === 6 && hasEmailParam)">
            <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
          </div>
        </div>
      </div>
    </div>
    <Footer :language="Language" :country="inputData.country_code.value" class="mt-2" />
  </div>
</template>

<style>
.layoutlp15 {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  width: 100%;
  overflow-x: hidden;
}

.animated-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  /* Lighter pink gradient */
  background: linear-gradient(135deg, #faf8f9 0%, #ffffff 50%, #f5f3f4 100%);
  z-index: -1;
}

/* Enhanced background with multiple layers */
.animated-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 40%, rgba(250, 219, 224, 0.4) 0%, rgba(255, 255, 255, 0) 60%), radial-gradient(circle at 70% 60%, rgba(252, 227, 231, 0.4) 0%, rgba(255, 255, 255, 0) 60%), radial-gradient(circle at 50% 50%, rgba(255, 251, 251, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0.8;
  animation: pulse 15s infinite alternate;
}

.animated-background::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ff69b4' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E"),
    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffb6c1' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  background-position: 0 0, 30px 30px;
  opacity: 0.6;
  animation: float 60s infinite linear;
}

/* Add floating hearts */
.animated-background .hearts {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

/* Create animated elements */
@keyframes pulse {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes float {
  0% {
    background-position: 0 0, 30px 30px;
  }
  100% {
    background-position: 100px 100px, 130px 130px;
  }
}

/* Add floating bubbles */
.bubble {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 192, 203, 0.12); /* Lighter pink bubbles */
  box-shadow: inset 0 0 10px rgba(255, 255, 255, 0.6);
  animation: rise 15s infinite ease-in;
  opacity: 0;
}

@keyframes rise {
  0% {
    bottom: -100px;
    transform: translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    bottom: 100%;
    transform: translateX(20px);
    opacity: 0;
  }
}

.content-wrapper {
  flex: 1 0 auto;
  position: relative;
  z-index: 1;
  background: transparent;
  display: flex;
  flex-direction: column;
}

.bg-transparent {
  background: transparent !important;
}

.lp15x {
  height: auto !important;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  background: transparent;
}

@media only screen and (max-width: 867px) {
  .layoutlp15 {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .content-wrapper {
    flex: 1 0 auto;
    display: flex;
    flex-direction: column;
  }

  .lp15x {
    flex: 1 0 auto;
    margin-bottom: 0 !important;
  }

  footer {
    position: relative;
    width: 100%;
    z-index: 1;
    margin-top: auto;
    background: transparent !important;
  }

  footer > div,
  footer .container,
  footer .row {
    background: transparent !important;
  }

  .animated-background {
    position: fixed;
    height: 100vh;
  }

  .side-image-container {
    display: none;
  }

  .content-box {
    width: 100% !important;
    max-width: 90vw !important;
    padding: 1rem !important;
  }

  .notification-box {
    margin-bottom: 1rem !important;
  }

  .button-group {
    gap: 1rem !important;
  }

  .container {
    padding: 0 0.5rem;
  }
}

/* Remove any potential white backgrounds from Bootstrap classes */
.container,
.container-fluid,
.row,
.col,
.col-md-5,
[class*="col-"] {
  background: transparent !important;
}

/* Add these styles */
footer,
footer.mt-2,
footer > *,
footer div {
  background: transparent !important;
}
</style>
