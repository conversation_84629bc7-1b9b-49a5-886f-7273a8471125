<script setup>
import { ref, onMounted, provide, watch } from "vue";
import Axios from "axios";
import PageOne from "../lp18/components/PageOne.vue";
import PageTwo from "../lp18/components/PageTwo.vue";
import PageThree from "../lp18/components/PageThree.vue";
import PageFour from "../lp18/components/PageFour.vue";
import PageFive from "../lp18/components/PageFive.vue";
import PageSix from "../lp18/components/PageSix.vue";
import PageSeven from "../lp18/components/PageSeven.vue";
import Navbar from "../../sites/lp18/components/views/NavBar.vue";
import Footer from "../../sites/lp18/components/views/Footer.vue";
import Endpoints from "./../../assets/js/config/endpoints.json";
import Params from "./../../assets/js/helper/urlParameters.js";
import Media from "./../../assets/js/helper/deviceDetection.js";
import Language from "./../../assets/js/helper/Language.js";
import inputData from "./../../assets/js/config/forms.json";
import config from "./../../assets/js/config/config.json";

const location = ref();
const device = ref(false);
const steps = ref(1);
const hasEmailParam = ref(false);

provide("assets", config.assets);

onMounted(async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailParam = urlParams.get("email");

  hasEmailParam.value = !!emailParam;
  if (hasEmailParam.value) {
    inputData.email = {
      value: emailParam,
      valid: true,
      error: false,
      required: true,
    };
  }
  await Axios.get(Endpoints.geo).then((s) => {
    device.value = s.data;
    inputData.location.value = s.data.geo.city ? s.data.geo.city : "Nevada";
    inputData.device.value = s.data;
    inputData.lander.value = Params().path;
    inputData.click_id.value = Params().click_id;
    inputData.source_id.value = Params().source_id;
    inputData.country.value = s.data.geo.country;
    inputData.country_code.value = s.data.geo.country_code.toLowerCase();
    inputData.locale.value = Params().locale;
    inputData.media.value = Media().device;
    location.value = inputData.location.value;
    inputData.http.value = Params().http;
    inputData.t1.value = Params().t1;
    inputData.t2.value = Params().t2;
    inputData.l.value = Params().l;
    inputData.s3.value = Params().s3;
    inputData.cmp.value = Params().cmp;
    inputData.image.value = Params().image;
    inputData.traf_id.value = Params().traf_id;
    inputData.tsid.value = Params().tsid;
    inputData.alreadyRegistered.value = Params().alreadyRegistered;
    if (s.data.geo.postal_code) {
      inputData.postal_code.value = s.data.geo.postal_code;
    }
  });

  window.history.pushState({ page: 1 }, null);
  window.addEventListener("popstate", handlePopstate);

  function handlePopstate() {
    window.location.href = config.links.back_button + Params().url;
  }
});

const moveNextSlide = () => {
  if (hasEmailParam.value && steps.value === 5) {
    steps.value = 7;
  } else {
    steps.value++;
  }
};

const moveBackSlide = () => {
  if (hasEmailParam.value && steps.value === 7) {
    steps.value = 5;
  } else {
    steps.value--;
  }
};

watch(steps, (newValue) => {
  console.log("Step changed to:", newValue);
});
</script>

<template>
  <div class="lp18-layout">
    <!-- Only show Navbar and Footer for steps other than 1 -->
    <Navbar v-if="steps !== 1" />

    <div class="page-container">
      <div v-if="steps === 1">
        <PageOne :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 2">
        <PageTwo :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 3">
        <PageThree :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 4">
        <PageFour :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 5">
        <PageFive :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 6 && !hasEmailParam">
        <PageSix :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
      <div v-else-if="steps === 7 || (steps === 6 && hasEmailParam)">
        <PageSeven :inputs="inputData" :steps="steps" :moveNextSlide="moveNextSlide" :moveBackSlide="moveBackSlide" :location="location" />
      </div>
    </div>

    <Footer v-if="steps !== 1" :language="Language" :country="inputData.country_code.value" class="mt-2" />
  </div>
</template>

<style>
/* LP18 Layout Styles */
.lp18-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: transparent;
  width: 100%;
  overflow-x: hidden;
}

.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: transparent;
}

/* Mobile responsive */
@media only screen and (max-width: 867px) {
  .lp18-layout {
    min-height: 100vh;
  }

  .page-container {
    flex: 1;
  }
}

/* Ensure transparent backgrounds */
.container,
.container-fluid,
.row,
.col,
[class*="col-"] {
  background: transparent !important;
}

footer,
footer.mt-2,
footer > *,
footer div {
  background: transparent !important;
}
</style>
