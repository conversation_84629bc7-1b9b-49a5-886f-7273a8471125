<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import Params from "../../../assets/js/helper/urlParameters.js";
import Language from "../../../assets/js/helper/Language.js";
import Searching from "../../../sites/lp8/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const selectedAge = ref(null);

const validationMessages = ref({ dob: null });

const handleAgeButtonClick = (ageGroup) => {
  selectedAge.value = ageGroup;
  if (ageGroup === "18-34") {
    window.location.href = "https://trakle02.online/tracker/100" + Params().url;
  }
};

const dob = (x, y) => {
  let min = parseInt(x);
  let max = parseInt(y);
  let random = Math.floor(Math.random() * (parseInt(max) - parseInt(min))) + min;
  props.inputs.birth_year.value = new Date().getFullYear() - (random + 1);
  props.inputs.birth_month.value = (Math.floor(Math.random() * (parseInt(12) - parseInt(1))) + 1).toString();
  props.inputs.birth_day.value = (Math.floor(Math.random() * (parseInt(29) - parseInt(1))) + 1).toString();

  props.inputs.birth_day.value.length == 1 ? (props.inputs.birth_day.value = "0" + props.inputs.birth_day.value) : "";
  props.inputs.birth_month.value.length == 1 ? (props.inputs.birth_month.value = "0" + props.inputs.birth_month.value) : "";

  props.moveNextSlide();
};

// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  // Use images from 120.webp to 150.webp (31 images total)
  const imageNumber = 120 + ((number - 1) % 31);
  return `${cdnBase}${imgPath}${imageNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}26.webp`);

// Add countdown functionality
const minutes = ref(1);
const seconds = ref(42);

const formattedTime = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 59;
    } else {
      clearInterval(timer);
    }
  }, 1000);
});
</script>
<template>
  <div class="five">
    <!-- Profile Gallery -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-wrapper">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 85%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Left Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Age Selection Content -->
        <div class="col-md-5 d-flex justify-content-center align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime }} to register!</p>
              </div>
            </div>
            <div class="clip">
              <h4 class="text-center fw-bold fs-6 text-lighth shadow-8d">{{ Language.local_area }}</h4>
              <p class="text-center text-lighti mt-3">{{ Language.access_website }}</p>
              <h3 class="text-center text-light8 shadow-8 mt-3">{{ Language.age_question }}</h3>

              <div class="btn-age px-3 mt-4">
                <div class="row mt-2">
                  <button type="button" class="btn btn-success border fw-bold text-lightb" @click="handleAgeButtonClick('18-34')">18+</button>
                </div>
                <div class="row mt-2">
                  <button type="button" class="btn btn-success border fw-bold text-lightb" @click="dob(35, 40)">35+</button>
                </div>
                <div class="row mt-2">
                  <button v-if="selectedAge !== 'Under 18'" type="button" class="btn btn-success border fw-bold text-lightb" @click="dob(40, 65)">40+</button>
                </div>
                <div class="row mt-2">
                  <button v-if="selectedAge !== 'Under 18'" type="button" class="btn btn-success border fw-bold text-lightb" @click="dob(65, 75)">65+</button>
                </div>
              </div>
              <Searching :location="location" :language="Language" />
              <p v-if="validationMessages.dob" class="text-light fs-6 mt-2">({{ validationMessages.dob }})</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <Steps :step="steps" />
  </div>
</template>

<style scoped>
/* Include styles from PageOne plus: */

.btn-age {
  max-width: 300px;
  margin: 0 auto;
}

.btn-age .btn {
  width: 100%;
  padding: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: #116d6e !important;
  color: #f6eca9 !important;
  font-size: 14px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-age .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-lighth {
  color: #282d4f !important;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.text-lighti {
  color: #a0204c !important;
  font-size: 16px !important;
  text-shadow: 3px 7px 8px rgba(134, 126, 126, 0.5);
}

.text-light8 {
  color: #282d4f !important;
  font-size: 24px !important;
}

.shadow-8 {
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
  font-size: 20px !important;
}

.text-lightb {
  color: #f6eca9 !important;
  font-size: 14px !important;
}

/* Update row margins */
.btn-age .row {
  margin-top: 0.5rem !important;
}

.btn-age .row:first-child {
  margin-top: 0.25rem !important;
}

.shadow-8d {
  font-size: 15px !important;
}

/* Keep the rest of the styles from PageOne */

/* Add responsive adjustments */
@media (max-width: 768px) {
  .btn-age {
    max-width: 250px;
  }

  .btn-age .btn {
    padding: 0.4rem;
    margin-bottom: 0.4rem;
    font-size: 13px !important;
  }

  .btn-age .row {
    margin-top: 0.4rem !important;
  }
}
</style>
