<script setup>
import { ref } from "vue";

const props = defineProps(["language"]);

const timer = ref(0);
let seconds = 60;
let counter = 1;

setInterval(function () {
    seconds == 0 ? (seconds = 60) : (seconds -= 1);
    seconds == 0 && counter > 0 ? counter-- : "";
    counter == 0 ? (counter = 1.0) : "";
    timer.value = "0" + counter + ":" + (seconds.toString().length == 1 ? (seconds = "0" + seconds) : seconds);
}, 1000);
</script>

<template>
    <div class="d-flex justify-content-center align-items-center timer8 flex-column p2 mt-2">
        <div class="fs-5 offer-text shadow-8 stroke">{{ language.offer_text }}</div>
        <div class="div">
            <span class="p-5 text-center text-light2 fs-5">{{ timer }}</span>
        </div>
    </div>
</template>

<style>
.timer8 {
    color: #b51b75 !important;
    background-color: rgba(232, 237, 225, 0.6);
    border-radius: 14px;
    padding: 10px;
}
.text-light2 {
    color: #b51b75 !important;
}

@media only screen and (max-width: 867px) {
    .rounded-circle8 {
        width: auto;
        height: 3em;
    }
    .offer-text {
        font-size: 12px !important;
    }
}
</style>
