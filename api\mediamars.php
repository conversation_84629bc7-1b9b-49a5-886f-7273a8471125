<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

$startTime = microtime(true);

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$traf_id = $_GET['traf_id'] ? $_GET['traf_id'] : "";
$md5Hash = md5($traf_id);
$traf_id = substr($md5Hash, 0, 16);

$offer	= isset($_GET['device']['geo']['country_code']) ? $_GET['device']['geo']['country_code'] : "";

$email		= $_GET['email'];
$username	= $_GET['username']; 
$birthday 	= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];
$client_link = "https://flirtfordate.co/api?utm_source=AdthorizedAPI&linkid=00012&payout=4.7&clickid=".$_GET['click_id']."&web_id=".$traf_id."&sub_id=".$_GET['t1'];

$data = [
	'email'		=> $email,
	'name'		=> $username,
	'password'	=> $_GET['password'],
	'gender'	=> $_GET['gender'],
	'birthday'		=> $birthday,
	'client_link'	=> $client_link,
];

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => json_encode($data),
	'response_data' => '',
	'source' => 'MediaMars '.$offer
];



$endpoint = $config->MediaMars->endpoint;
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $endpoint);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$responseData = json_decode($response, true);

print_r($response);
print_r($httpCode);
die;

// Do not include Email in data storage
$data['email'] = '************';

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= json_encode($response);
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $trackingUrl;
$return['time']				= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['subid'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['dob'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';


// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);