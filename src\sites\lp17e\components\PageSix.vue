<script setup>
import { ref, defineProps, inject, onMounted } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import config from "../../../assets/js/config/config.json";

const props = defineProps({
  steps: Number,
  moveNextSlide: Function,
  moveBackSlide: Function,
  language: Object,
  location: String,
  inputs: Object,
  emailValidated: Boolean,
});

const steps = ref(props.steps);
const validEmail = ref(false);
const assets = inject("assets");

onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const emailFromParams = urlParams.get("email");

  if (emailFromParams) {
    props.inputs.email.value = emailFromParams;
    setTimeout(() => {
      const emailInput = document.getElementById("email");
      if (emailInput) {
        emailInput.value = emailFromParams;
      }
    }, 0);
  }
});

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const isValid = emailPattern.test(props.inputs.email.value);
  validEmail.value = isValid;
  props.inputs.email.valid = isValid;

  // If email is valid, move to next step immediately
  if (isValid) {
    props.moveNextSlide();
  }
  return isValid;
};

const continueToNextSlide = () => {
  if (!validateEmail()) {
    const alertMessage = document.getElementById("alert-message").innerText;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
  }
};

const cdnBase = config.cdn;
const imgPath = config.images.img;
const cardPhotoUrl = `${cdnBase}${imgPath}143.webp`;
</script>

<template>
  <div class="container insta-container">
    <div class="insta-card">
      <div class="insta-header">
        <img class="insta-avatar" :src="assets + 'sexydates.png'" alt="Profile" />
      </div>
      <div class="insta-photo">
        <img class="insta-photo-img" :src="cardPhotoUrl" alt="Instagram Photo" />
      </div>
      <div class="insta-content">
        <form>
          <div class="form-group">
            <label for="gender" class="w-100 text-center fs-5 insta-label">{{ Language.email_text }}</label>
            <hr />
            <div class="d-flex justify-content-around my-3">
              <div class="d-flex justify-content-center flex-column gap-3 w-100">
                <input v-model="props.inputs.email.value" id="email" class="form-control rounded-5 px-5" placeholder="e.g. <EMAIL>" type="text" @keypress="handleKeyPress" />
                <button type="button" class="btn insta-btn w-100" for="option2" @click="continueToNextSlide">{{ Language.continue }}</button>
                <div class="text-center">
                  <p class="text-dark" style="font-size: 10px">{{ Language.spam_alert }}</p>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div id="alert-message" style="display: none">
          {{ Language.email_error }}
        </div>
        <Steps :step="steps" />
        <div class="disclaimer mt-2">
          <div class="progress mb-3 insta-progress" role="progressbar" aria-label="Info example" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar insta-bar" style="width: 84%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.insta-container {
  min-height: 85vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  background: transparent;
  padding-top: 1rem;
}

.insta-card {
  background: #fff;
  border-radius: 1.2em;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  width: 340px;
  /* width: 100%; */
  overflow: hidden;
  padding-bottom: 0.2em;
}

.insta-header {
  display: flex;
  align-items: center;
  padding: 1em 1em 0.5em 1em;
  background: #fff;
}
.insta-avatar {
  width: 150px;
  height: auto;
  object-fit: cover;
  margin-right: 0.7em;
}
.insta-username {
  font-weight: 600;
  color: #262626;
  font-size: 1.1em;
}

.insta-photo {
  width: 100%;
  aspect-ratio: 1 / 0.8;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.insta-photo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insta-content {
  padding: 0.8em 1em 0.3em 1em;
}
.insta-label {
  font-weight: 500;
  color: #262626;
  font-size: 1.1em;
}
.insta-btn {
  width: 7em;
  height: 2.7em;
  border: none;
  border-radius: 1.2em;
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1em;
  box-shadow: 0 2px 8px rgba(225, 48, 108, 0.1);
  transition: background 0.2s, color 0.2s, transform 0.1s;
}
.insta-btn:hover {
  background: linear-gradient(90deg, #fdc468 0%, #e1306c 100%);
  color: #fff;
  transform: translateY(-2px) scale(1.04);
}
.insta-progress {
  background: #f7f7f7;
  border-radius: 1em;
}
.insta-bar {
  background: linear-gradient(90deg, #e1306c 0%, #fdc468 100%) !important;
}
.disclaimer {
  text-align: center;
  margin-top: 1em;
}
@media (max-width: 500px) {
  .insta-container {
    padding-top: 1rem;
    min-height: 100vh;
  }
  .insta-card {
    max-width: 98vw;
    border-radius: 0.7em;
  }
  .insta-content {
    padding: 0.7em 0.3em 0.3em 0.3em;
  }
  .insta-header {
    padding: 0.7em 0.5em 0.5em 0.5em;
  }
}
</style>
