<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);


$url = 'https://trakle02.online/api/client_api';

$data = [
    "tsid" => "1525548",
    "username" => "denben3",
    "password" => "05073fc666c700",
    "dob" => "1973-03-25",
    "ip" => "*************",
    "email" => "<EMAIL>",
    "gender" => "male",
    "searching" => "female"
];

$headers = [
    'X-HTTP-AUTH-KEY: i2PcDzFNGrvOtKYtdjk14Qbropiy5b7BibylcgZLL4XV6O2KlYmJqtA4S4oy557V',
    'Content-Type: application/json'
];

$ch = curl_init($url);

curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

$response = curl_exec($ch);

if (curl_errno($ch)) {
    echo 'Curl error: ' . curl_error($ch);
} else {
    echo 'Response: ' . $response;
}

curl_close($ch);

exit;