<template>
  <nav class="navbar">
    <div class="nav-left">
      <i class="fas fa-camera nav-icon"></i>
    </div>

    <div class="nav-center">
      <div class="logo-container">
        <img :src="assets + 'sexydates.png'" alt="Logo" class="logo-image" />
      </div>
    </div>

    <div class="nav-right">
      <i class="fas fa-sync-alt nav-icon"></i>
      <i class="fas fa-paper-plane nav-icon"></i>
    </div>
  </nav>
</template>

<script setup>
import { inject } from "vue";
const assets = inject("assets");
</script>

<style scoped>
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #a855f7 0%, #06b6d4 25%, #ec4899 50%, #8b5cf6 75%, #06b6d4 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-left,
.nav-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.nav-icon {
  font-size: 1.4rem;
  color: white;
  cursor: pointer;
  transition: opacity 0.2s;
}

.nav-icon:hover {
  opacity: 0.8;
}

.nav-center {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-image {
  height: 2.5rem;
  width: auto;
  object-fit: contain;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  color: white;
  font-family: "Arial", sans-serif;
}

.login-btn {
  background-color: #db1d86;
  color: white;
  border: none;
  padding: 0.5rem 1.2rem;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.login-btn:hover {
  background-color: #42061f;
}

@media (max-width: 768px) {
  .navbar {
    padding: 0.6rem 1rem;
  }

  .logo-text {
    font-size: 1.4rem;
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .login-btn {
    padding: 0.4rem 1rem;
    font-size: 0.9rem;
  }
}
</style>
