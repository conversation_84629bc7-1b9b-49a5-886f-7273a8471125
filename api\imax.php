<?php
include_once('./log.php');

$config = file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;

/**
 * Make sure the configuration is not missing or empty
 */
if($config == null){
	$fileMissing = json_encode([
		'code' => 401,
		'message' => 'Api configuration file missing',
	]);
	die($fileMissing);
}

$startTime = microtime(true);
$traf_id = $_GET['traf_id'] ? substr(md5($_GET['traf_id']), 0, 16) : "";
$offer = $_GET['offer'] ? $_GET['offer'] : 'us';

$data = [
    "email" => $_GET['email'],
    "profile"       => [
        "name"      => isset($_GET['username']) ? $_GET['username'] : '',
        "birthday"  => $_GET['dob'],
        "gender"    => [
            "id"    => isset($_GET['gender']) ? (strtolower($_GET['gender']) === 'male' ? 1 : (strtolower($_GET['gender']) === 'female' ? 2 : null)) : null
        ],
        "gender_search" => [
            "id" => isset($_GET['looking']) ? (strtolower($_GET['looking']) === 'male' ? 1 : (strtolower($_GET['looking']) === 'female' ? 2 : null)) : null
        ],
		"city" => isset($_GET['city']) ? $_GET['city'] : ''
    ],
    "affiliate" => [
        "aff_id"    => 15601,
        "offer_id"  => $config->imax->$offer->offer_id,
        "aff_sub"   => $_GET['t2'],
        "source"    =>  $_GET['t1'],
        "ext_id"    => $_GET['click_id']
    ]
];

$endpoint = $config->imax->$offer->endpoint;

$ch = curl_init($endpoint);
curl_setopt_array($ch, array(
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($data, JSON_UNESCAPED_UNICODE),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json',
        'X-HTTP-AUTH-KEY: '.$config->imax->api_key,
    )
));

$response = curl_exec($ch);
$responseData = json_decode(trim($response), true);
$httpcode = curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
curl_close($ch);

// Do not include Email in data storage
$data['email'] = '************';

$return = [
	'code' => 200,
	'message' => '',
	'url' => '',
	'time' => '0',
	'request_data' => $data,
	'response_data' => '',
	'source' => 'imax - ' . $_GET['offer']
];


/**
 * 1. Check if user created successfully - build success response
 * 2. If user creation failed - build error response
 */
if(isset($responseData['status']) && $responseData['status'] == 'success' && isset($responseData['data']['login_url'])){
	$return['message'] = $responseData['status'];
	$return['code'] = isset($responseData['code']) ? $responseData['code'] : null;
	$return['url'] = $responseData['data']['login_url'];
}else{
	$return['message'] = isset($responseData['errors'][0]['message']) ? $responseData['errors'][0]['message'] :'Cant connect to api';
	$return['code']	= isset($responseData['code']) ? $responseData['code'] : null;
}

$endTime = microtime(true);
$return['key']				= $_GET['key'];
$return['response_data']	= $responseData;
$return['start_time']		= $startTime;
$return['end_time']			= $endTime;
$return['endpoint']			= $endpoint;
$return['time'] 			= number_format( ( $endTime - $startTime ), 2);
$return['click_id']			= $_GET['click_id'];
$return['source_id']		= $_GET['source_id'];
$return['is_sellable']		= $_GET['is_sellable'];

$return['email']			= $_GET['email'];
$return['username']			= $_GET['username'];
$return['password']			= $_GET['password'];
$return['gender']			= $_GET['gender'];
$return['looking']			= $_GET['looking'];
$return['dob']				= $_GET['birth_year'].'-'.$_GET['birth_month'].'-'.$_GET['birth_day'];

$return['device']			= $_GET['device'];
$return['city']				= $_GET['device']['geo']['city'];
$return['lander']			= $_GET['lander'];
$return['http']				= $_GET['http'];
$return['index_id']			= $_GET['index_id'];
$return['antifraud']		= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';

// Send Data to Log API
$log	= new Log($return);
$send	= $log->send($config->log->endpoint);

echo json_encode($return);