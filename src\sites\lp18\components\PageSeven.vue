<script setup>
import { ref, defineProps, onMounted, computed } from "vue";
import Steps from "../../lp9/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Api from "../../../assets/js/helper/api.js";
import Swal from "sweetalert2";
import Searching from "../../lp14/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";
import Params from "../../../assets/js/helper/urlParameters.js";

const props = defineProps(["inputs", "steps", "moveNextSlide"]);
const steps = ref(props.steps);
const apiErrorMessage = ref(false);
const disableSubmitBtn = ref(false);
const showSearching = ref(false);

const validationMessages = ref({
  username: false,
  dob: false,
  email: false,
  password: false,
  privacy: false,
  privacy_service: false,
});

// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  // Use images from 120.webp to 150.webp (31 images total)
  const imageNumber = 120 + ((number - 1) % 31);
  return `${cdnBase}${imgPath}${imageNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}31.webp`);

onMounted(() => {
  window.dataLayer?.push({
    event: "page_view",
  });

  const uncheckedCountries = ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "AD", "BA", "IS", "LI", "MC", "MD", "ME", "MK", "NO", "RS", "CH", "UA", "GB", "VA", "SM"];

  if (props.inputs.device.value?.geo?.country_code && !uncheckedCountries.includes(props.inputs.device.value.geo.country_code)) {
    props.inputs.privacy.value = true;

    const privacyCheckbox = document.getElementById("privacyCheck");
    if (privacyCheckbox) {
      privacyCheckbox.checked = true;
    }
  }

  // Check if this page was opened to submit form
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get("submitForm")) {
    // Get stored form data
    const storedFormData = JSON.parse(sessionStorage.getItem("formData"));
    if (storedFormData) {
      // Clear stored data
      sessionStorage.removeItem("formData");
      // Make API call
      Api(storedFormData, disableSubmitBtn, apiErrorMessage);
    }
  }
});

const showLocationConsent = computed(() => {
  const californiaLocations = [
    "Los Angeles",
    "San Diego",
    "Santa Monica",
    "San Francisco",
    "San Jose",
    "Fresno",
    "Sacramento",
    "Long Beach",
    "Oakland",
    "Bakersfield",
    "Anaheim",
    "Santa Ana",
    "Riverside",
    "Stockton",
    "Chula Vista",
    "Irvine",
    "Fremont",
    "San Bernardino",
    "Modesto",
    "Oxnard",
    "Fontana",
  ];

  return californiaLocations.includes(props.inputs.location.value);
});

const validateForm = async (event) => {
  // Push event to trigger GTM tag
  window.dataLayer?.push({
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
    "gtm.element": event.target,
    "gtm.elementClasses": "button_click g-recaptcha btn border btn-outline-12 rounded-5 px-5",
    "gtm.elementId": "submit-button",
  });

  // Log for debugging
  console.log("GTM Event Pushed:", {
    event: "button_click",
    Click_Classes: "button_click",
    _event: "gtm.linkClick",
    _triggers: "203887914_4",
  });

  if (!validateUsername()) {
    return false;
  }
  if (!validateDOB()) {
    return false;
  }
  if (!validateEmail()) {
    return false;
  }
  if (!validatePassword()) {
    const alertMessage = validationMessages.value.password;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }
  if (!validatePrivacy()) {
    const alertMessage = validationMessages.value.privacy;
    Swal.fire({
      text: alertMessage,
      customClass: {
        popup: "custom-swal-popup",
      },
    });
    return false;
  }

  // Show searching screen
  showSearching.value = true;

  // Prepare form data
  let formData = {};
  for (let x in props.inputs) {
    formData[x] = props.inputs[x]["value"];
  }

  // Wait for 1 second before making API call
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Make API call first
  Api(formData, disableSubmitBtn, apiErrorMessage);

  // Open Google in new tab after API call END
  // window.open("https://trakle01.online/tracker/113" + Params().url);

  return true;
};

const validatePrivacy = () => {
  if (props.inputs.privacy.value == false) {
    validationMessages.value.privacy = Language.alert_update;
    return false;
  } else {
    validationMessages.value.privacy = false;
    return true;
  }
};

const validatePassword = () => {
  let x = props.inputs.password.value;
  if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
    validationMessages.value.password = Language.password_error_2;
    return false;
  } else {
    validationMessages.value.password = false;
    return true;
  }
};

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const validateUsername = () => {
  let x = props.inputs.username.value;
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(x)) {
      validationMessages.value.username = Language.username_error_3;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  } else {
    if (x.length > 14 || x.length < 6 || validator(x) == false || explicitValidator(x)) {
      validationMessages.value.username = Language.username_error_2;
      return false;
    } else {
      validationMessages.value.username = false;
      return true;
    }
  }
};

const validateEmail = () => {
  if (/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,})+$/.test(props.inputs.email.value)) {
    validationMessages.value.email = false;
    return true;
  } else {
    validationMessages.value.email = Language.email_error;
    return false;
  }
};

const validator = (str) => {
  return /[0-9][a-zA-Z]|[a-zA-Z][0-9]/.test(str);
};

const explicitValidator = (str) => {
  const format = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/;
  return format.test(str);
};

const validateDOB = () => {
  if (props.inputs.birth_day.value == "" || props.inputs.birth_year.value == "" || props.inputs.birth_month.value == "") {
    validationMessages.value.dob = Language.select_your_age;
    return false;
  } else {
    validationMessages.value.dob = false;
    return true;
  }
};

// Add countdown functionality
const minutes = ref(1);
const seconds = ref(34);

const formattedTime = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 59;
    } else {
      clearInterval(timer);
    }
  }, 1000);
});
</script>

<template>
  <Searching v-if="showSearching" />

  <div class="seven">
    <!-- Profile Gallery -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-wrapper">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 100%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Left Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Password and Terms Form -->
        <div class="col-md-5 d-flex justify-content-center align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime }} to register!</p>
              </div>
            </div>
            <div class="clip">
              <h3 class="text-center text-light8 shadow-8 mt-3">{{ Language.password_create }}</h3>

              <div class="form-section mt-2">
                <div class="input-group">
                  <input type="password" id="form12" class="form-control custom-input" placeholder="Password" v-model="inputs.password.value" />
                </div>

                <div class="terms-section mt-2">
                  <div class="notice">
                    <p>
                      <strong>{{ Language.basic_info }}</strong> <br />
                      <strong>{{ Language.data_controller }}</strong> {{ Language.leads }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.purpose }}</strong> {{ Language.data_response }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.rights }}</strong
                      >{{ Language.data_access }} <a class="cursor-pointer text-danger" href="/privacy">{{ Language.info }}</a> <br />
                      <strong>{{ Language.additional_info }}</strong
                      >{{ Language.data_protect }}
                      <a class="cursor-pointer text-danger" href="/privacy">{{ Language.privacy_policy }}</a>
                    </p>
                  </div>

                  <div class="accept-terms">
                    <div class="checkbox-group">
                      <input type="checkbox" id="privacyCheck" v-model="inputs.privacy.value" class="custom-checkbox" />
                      <label for="privacyCheck" class="agree">
                        {{ Language.privacy_agree }}
                      </label>
                    </div>
                  </div>

                  <div v-if="showLocationConsent" class="location-consent mt-3">
                    <input class="form-check-input" type="checkbox" id="flexCheckDefault8" v-model="inputs.is_sellable.value" />
                    <label for="flexCheckDefault8" class="text-secondary">
                      {{ Language.data_permission }}
                    </label>
                  </div>
                </div>

                <div class="submit-section mt-4">
                  <button class="btn next-btn" @click="(event) => validateForm(event)" :class="{ disabled: disableSubmitBtn }">
                    {{ Language.btn_submit }}
                    <div class="spinner-border spinner-grow-sm text-light" v-show="disableSubmitBtn" role="status">
                      <span class="visually-hidden">{{ Language.loading }}</span>
                    </div>
                  </button>

                  <p class="spam-alert mt-2">{{ Language.spam_alert }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="alert-message3" style="display: none">{{ Language.alert_update }}</div>
    <div id="alert-message" style="display: none">{{ Language.alert_update }}</div>
    <div id="alert-message2" style="display: none">{{ Language.password_error_2 }}</div>

    <Steps :step="steps" />
  </div>
</template>

<style scoped>
/* Include styles from PageOne plus: */

.form-section {
  max-width: 400px;
  margin: 0 auto;
}

.custom-input {
  border: 1px solid #dee2e6;
  padding: 0.75rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 100%;
}

.custom-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.terms-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  /* padding: 1rem; */
  text-align: center;
}

.notice {
  font-size: 6px !important;
  margin-bottom: 1rem;
}

.accept-terms {
  font-size: 13px !important;
  text-align: start !important;
}

.checkbox-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.custom-checkbox {
  width: 13px;
  height: 13px;
  margin-top: 0;
}

.agree {
  font-size: 14px;
  line-height: 20px;
}

.next-btn {
  background-color: #116d6e !important;
  color: #f6eca9 !important;
  font-size: 14px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
  width: 100%;
  padding: 0.75rem;
}

.next-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.next-btn.disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.spam-alert {
  color: #666;
  font-size: 10px;
  text-align: center;
}

.text-light8 {
  color: #282d4f !important;
  font-size: 24px !important;
}

.shadow-8 {
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
}

.location-consent {
  font-size: 11px;
}

/* Keep the rest of the styles from PageOne */
</style>
