<?php
include_once('./log.php');
include_once('./helpers/execution_time.php');

class Plaxy {


	private $config;
	private $data;
	private $return;
	private $execStats;
	private $template;
	private $log;

	/**
	 * Bidding Specific Properties
	 * Consists of the following HTTP Response:
	 * ----------------------------------------
	 * Bid Request, Bid Placement
	 * ----------------------------------------
	 * ----------------------------------------
	 * URL for Bid Redirection (for clicks) and Bid Placement
	 */
	private $bidRequestCode;
	private $bidPlacementCode;
	private $bidRedirectionUrl;
	private $bidPlacementUrl;

	/**
	 * Assign Get Bid Details into property
	 */
	private $bitPriceData;

	/**
	 * RTB Response Dictonary Object
	 */
	private $rtbCodeDictonary;


	public function __construct() {
		$config         		= @file_exists('./config/api.json') ? json_decode(file_get_contents('./config/api.json')) : null;
		$this->rtbCodeDictonary = json_decode(file_get_contents('./config/rtb_code_dictionary.json'));


        if($config == null){
            $fileMissing = json_encode([
                'code'      => 401,
                'message'   => 'Api configuration file missing',
            ]);
            die($fileMissing);
        }

        // Prepare data fetch from $_GET
        // Prepare Configuration details
        $this->data     = json_decode(json_encode($_GET['data'], true));
        $this->config   = $config->RTB->Plaxy->{$this->data->device->geo->country_code} ?? $config->RTB->Plaxy->WW;
		$this->log		= $config->log;
	}

	
	public function getBidPrice() {

        $this->template = [
            'id'    => $this->data->click_id,
            'imp'   => [
                [
                    'id' => $this->data->click_id,
                ]
            ],
            'device' => [
                'ua' => $this->data->device->raw,
                'ip' => $this->data->device->ip,
                'devicetype' => 2,
            ],
            'user' => [
                'id'        => $this->data->click_id,
                'yob'       => $this->data->birth_year,
                'gender'    => $this->data->gender === 'female' ? 'F' : 'M',
                'ext'       => [
                    'email' => $this->data->email,
                    'lang'  => $this->data->locale,
                ]
            ],
            'test' => 0,
            'cur' => ['USD'],
            'ext' => [
                'cid'       => $this->config->zone_id,
                'clickid'   => $this->data->click_id,
                'email'     => $this->data->email,
                'email_encoded' => base64_encode($this->data->email),
                'lang'      => $this->data->locale,
                'utm_source'    => $this->data->traf_id,
            ]
        ];



		if(isset($this->data->traf_id)){
			$this->template['ext']['sub'] = isset($this->data->traf_id) ? substr(md5($this->data->traf_id), 0, 16) : "";
		}

		// Add Tier 3 countries
		if(isset($this->data->rules->price) && !is_null($this->data->rules->price) && (float)$this->data->rules->price > 0){
			$this->template['imp'][0]['bidfloor']	= (float) $this->data->rules->price;
		}

		return $this->initiateBidPriceCall();
	}

	
	private function initiateBidPriceCall() {

		$baseUrl    = $this->config->endpoint;
        $authParams = http_build_query([
            'affId'     => $this->config->account_id,
            'secretId'  => $this->config->api_key,
            'cid'       => $this->config->zone_id
        ]);
        
        $ch = curl_init($baseUrl . '?' . $authParams);
        
        $headers = [
            'Content-Type: application/json',
            'Authorization: ' . $this->config->account_id . ' ' . $this->config->api_key,
            'affId: ' . $this->config->account_id,
            'secretId: ' . $this->config->api_key
        ];

        curl_setopt_array($ch, array(
            CURLOPT_POST        => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER  => $headers,
            CURLOPT_POSTFIELDS  => json_encode($this->template)
        ));

        $response = curl_exec($ch);
        $responseData = json_decode($response, TRUE);
        $this->bidRequestCode	= curl_getinfo($ch, CURLINFO_RESPONSE_CODE);
        curl_close($ch);

		// Set nURL and ADM
		if($this->bidRequestCode == 200){
			$this->bidRedirectionUrl	= $responseData['seatbid'][0]['bid'][0]['adm'] ?? false;
			$this->bidPlacementUrl		= $responseData['seatbid'][0]['bid'][0]['burl'] ?? false;
		}
		// if($this->bidRequestCode == 200){
		// 	$this->bidRedirectionUrl = $responseData->seatbid[0]->bid[0]->adm ?? false;
		// 	$this->bidPlacementUrl   = $responseData->seatbid[0]->bid[0]->nurl ?? false;
		// }
		
		return json_decode(json_encode($responseData));
	}


	public function initiatePlacingBid($responseData){
		return $this->handleResponse($responseData);
	}

	
	private function handleResponse($responseData) {
		$this->return = [
			'code' => 200,
			'message' => '',
			'url' => '',
			'time' => '0',
			'request_data' => $this->template,
			'response_data' => '',
			'source' => 'Plaxy - '.$this->data->device->geo->country_code
		];

		if($this->bidRequestCode == 200){
			$this->return['message']  = 'Bid placed successfully';
			$this->return['code']     = $this->bidRequestCode;
			
			$this->confirmBid($responseData);
			if($this->bidPlacementCode == 200){
				$this->sendPostback($responseData);
			}
		}else if(isset($this->rtbCodeDictonary->{$this->bidRequestCode})){
			$this->return['message']	= $this->rtbCodeDictonary->{$this->bidRequestCode};
			$this->return['code']		= $this->bidRequestCode;
		}else{
			$this->return['message']  = 'Unknown RTB Error Code';
			$this->return['code']     = $this->bidRequestCode;
		}
		
		return $this->prepareReturnData($responseData);
	}

	
	private function confirmBid($responseData) {
		if($this->bidRequestCode == 200){
			$copen = curl_init($this->bidPlacementUrl);
			curl_setopt($copen, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($copen, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($copen, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($copen, CURLOPT_HTTPHEADER, [
				'x-openrtb-version: 2.4',
				'Connection: Keep-Alive',
				'Authorization: Bearer '.$this->config->api_key,
				'Content-Type: application/json'
			]);
			$response = curl_exec($copen);
			$this->bidPlacementCode = curl_getinfo($copen, CURLINFO_RESPONSE_CODE);
			curl_close($copen);

			$responseData = json_decode($response, true);

			// Check and see if bid placement is successful
			if(in_array($this->bidPlacementCode, [200, 201]) && isset($responseData['redirectUrl'])){
				// ADM Trigger click URL
				$this->return['url'] = $responseData['redirectUrl'];
				return true;
			}

			return false;
		}
	}
	
	
	private function sendPostback($responseData) {
		if($this->bidPlacementCode == 200){
			$postbackUrl = "https://trakle02.online/postback?id=" . urlencode($this->data->click_id) . 
				"&payout=" . urlencode($responseData->seatbid[0]->bid[0]->price) . 
				"&currency=USD" .
				"&src=" . urlencode("plaxy").
				"&type=" . urlencode("rtb").
				"&country_code=" . urlencode($this->data->device->geo->country_code);

			$postbackCurl = curl_init();
			curl_setopt($postbackCurl, CURLOPT_URL, $postbackUrl);
			curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($postbackCurl, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($postbackCurl, CURLOPT_RETURNTRANSFER, true);
			curl_exec($postbackCurl);
			curl_close($postbackCurl);
		}
	}

	
	private function prepareReturnData($responseData) {

		$this->execStats	= EXEC_TIME->stats();
		$this->return['key'] 				= $this->data->key;
		$this->return['response_data'] 		= $responseData;
		$this->return['start_time'] 		= $this->execStats->start;
		$this->return['end_time'] 			= $this->execStats->end;
		$this->return['time'] 				= $this->execStats->duration;
		$this->return['endpoint'] 			= $this->config->endpoint;
		$this->return['click_id']         	= $this->data->click_id;
		$this->return['source_id']        	= $this->data->source_id;
		$this->return['is_sellable']      	= $this->data->is_sellable;
		$this->return['email']            	= $this->data->email;
		$this->return['username']         	= $this->data->username;
		$this->return['password']         	= $this->data->password;
		$this->return['gender']           	= $this->data->gender;
		$this->return['looking']          	= $this->data->seek;
		$this->return['dob']              	= $this->data->birth_year .'-'.$this->data->birth_month .'-'.$this->data->birth_day;
		$this->return['device']           	= $this->data->device;
		$this->return['city']             	= $this->data->location;
		$this->return['lander']           	= $this->data->lander;
		$this->return['http']             	= $this->data->http;
		$this->return['antifraud']			= isset($_GET['antifraud']) ? json_encode($_GET['antifraud']) : '';
		
		// Send Data to Log API
		$log    = new Log($this->return);
		$send   = $log->send($this->log->endpoint);
		
		return json_encode($this->return);
	}


}


define('Plaxy', new Plaxy);