<script setup>
defineProps(["language", "country"]);
</script>

<template>
  <div class="footer-wrapper">
    <!-- Instagram-style Navigation Bar -->
    <div class="instagram-nav-container">
      <div class="instagram-nav">
        <div class="nav-button">
          <i class="fas fa-home"></i>
        </div>
        <div class="nav-button">
          <i class="fas fa-search"></i>
        </div>
        <div class="nav-button">
          <i class="fas fa-plus-square"></i>
        </div>
        <div class="nav-button">
          <i class="fas fa-heart"></i>
        </div>
        <div class="nav-button">
          <i class="fas fa-user"></i>
        </div>
      </div>
    </div>
    
    <footer class="footer-content">
      <ul class="nav justify-content-center mb-3">
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-solid fa-shield fs-6 nav-icon"></i>
            <label class="pt-1"><small>{{ language.secure }}</small></label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-solid fa-shield-halved fs-6 nav-icon"></i>
            <label class="pt-1"><small>{{ language.private }}</small></label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-brands fa-expeditedssl fs-6 nav-icon"></i>
            <label class="pt-1"><small>{{ language.safe }}</small></label>
          </p>
        </li>
        <li class="nav-item text-center px-4">
          <p>
            <i class="fa-brands fa-cc-visa fs-6 nav-icon"></i>
            <label class="pt-1"><small>{{ language.payment }}</small></label>
          </p>
        </li>
      </ul>
      
      <ul class="nav justify-content-center links-section mb-2">
        <li class="nav-item">
          <a href="/privacy" target="_blank" class="nav-link px-2">{{ language.privacy }}</a>
        </li>
        <li class="nav-item">
          <a href="/terms" target="_blank" class="nav-link px-2">{{ language.terms }}</a>
        </li>
        <li class="nav-item" v-if="country == 'us'">
          <a href="/ccpa" target="_blank" class="nav-link px-2">{{ language.ccpa }}</a>
        </li>
      </ul>
      
      <p class="text-center copyright">Sexy-Dates &copy; Copyright 2024</p>
    </footer>
  </div>
</template>

<style scoped>
/* Instagram Nav Styles */
.instagram-nav-container {
  width: 100%;
  background-color: white;
  padding: 15px 0;
  border-top: 1px solid #dbdbdb;
  border-bottom: 1px solid #dbdbdb;
  margin-bottom: 20px;
}

.instagram-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 500px;
  margin: 0 auto;
  padding: 0 20px;
}

.nav-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  cursor: pointer;
}

.nav-button i {
  font-size: 1.5rem;
  color: #262626;
}

/* Remove fixed positioning and margin adjustment */
.footer-wrapper {
  margin-bottom: 0;
}

.footer-wrapper {
  margin-top: 2rem;
  background: linear-gradient(90deg, #6d0832 0%, #970d0d 100%);
  color: white;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem 1rem;
}

.nav-icon {
  color: white;
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.nav-item p {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0;
}

.nav-item label {
  color: white;
  font-size: 0.8rem;
  opacity: 0.9;
}

.links-section {
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 1rem;
}

.nav-link {
  color: white !important;
  opacity: 0.8;
  font-size: 0.8rem;
  transition: opacity 0.2s;
}

.nav-link:hover {
  opacity: 1;
}

.copyright {
  color: white;
  opacity: 0.8;
  font-size: 0.8rem;
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .footer-content {
    padding: 1rem 0.5rem;
  }

  .nav-item {
    padding: 0 0.5rem !important;
  }

  .nav-icon {
    font-size: 1.2rem;
  }

  .nav-item label {
    font-size: 0.7rem;
  }

  .nav-link {
    font-size: 0.7rem;
  }

  .instagram-nav {
    max-width: 100%;
    padding: 0 10px;
  }
  
  .nav-button i {
    font-size: 1.3rem;
  }
}
</style>
