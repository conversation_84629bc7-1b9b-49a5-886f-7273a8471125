<script setup>
import { ref, onMounted, defineProps, computed } from "vue";
import Steps from "../../lp8/components/views/StepsMarker.vue";
import Language from "../../../assets/js/helper/Language.js";
import Swal from "sweetalert2";
import Searching from "../../../sites/lp8/components/views/Searching.vue";
import config from "../../../assets/js/config/config.json";

const props = defineProps(["steps", "moveNextSlide", "moveBackSlide", "language", "location", "inputs"]);
const steps = ref(props.steps);
const isInputFocused = ref(false);

const validationMessages = ref({
  username: null,
});

const urlParams = new URLSearchParams(window.location.search);
const isUsernameZero = urlParams.get("username") === "all";

const continueToNextSlide = () => {
  const usernameRegex = /^(?=.*[0-9])(?=.*[a-zA-Z]).{6,14}$/;
  const usernameSimpleRegex = /^.{6,14}$/;
  const alertMessage = document.getElementById("alert-message").innerText;

  if (isUsernameZero) {
    if (!usernameSimpleRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: "Your username should be between 6 and 14 characters.",
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  } else {
    if (!usernameRegex.test(props.inputs.username.value)) {
      Swal.fire({
        text: alertMessage,
        customClass: {
          popup: "custom-swal-popup",
        },
      });
    } else {
      props.moveNextSlide();
    }
  }
};

const onFocus = () => {
  isInputFocused.value = true;
};

const onBlur = () => {
  isInputFocused.value = false;
};

const handleKeyPress = (event) => {
  if (event.key === "Enter") {
    continueToNextSlide();
  }
};

// Construct CDN URLs
const cdnBase = config.cdn;
const imgPath = config.images.img;
const getProfileImageUrl = (number) => {
  // Use images from 120.webp to 150.webp (31 images total)
  const imageNumber = 120 + ((number - 1) % 31);
  return `${cdnBase}${imgPath}${imageNumber}.webp`;
};
const sideImageUrl = computed(() => `${cdnBase}${imgPath}19.webp`);
// Add countdown functionality
const minutes = ref(1);
const seconds = ref(45);

const formattedTime = computed(() => {
  return `${minutes.value} minute${minutes.value !== 1 ? "s" : ""} ${seconds.value} second${seconds.value !== 1 ? "s" : ""}`;
});

onMounted(() => {
  const timer = setInterval(() => {
    if (seconds.value > 0) {
      seconds.value--;
    } else if (minutes.value > 0) {
      minutes.value--;
      seconds.value = 59;
    } else {
      clearInterval(timer);
    }
  }, 1000);
});
</script>

<template>
  <div class="one">
    <!-- Profile Gallery (same as PageOne) -->
    <div class="profile-gallery mt-2">
      <div class="gallery-container">
        <div class="profile-scroll-wrapper">
          <div v-for="n in 20" :key="n" class="profile-item">
            <div class="profile-image-container">
              <img :src="getProfileImageUrl((n % 30) + 1)" alt="Profile" class="profile-image" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Progress Bar -->
    <div class="progress-container">
      <div class="progress-wrapper">
        <div class="progress">
          <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>

    <div class="container mt-5">
      <div class="d-flex justify-content-center align-items-center">
        <!-- Left Image Container -->
        <div class="side-image-container">
          <img :src="sideImageUrl" alt="Side Image" class="side-image" />
          <div class="post-controls">
            <div class="post-actions">
              <div class="action-buttons">
                <button class="action-btn">
                  <i class="far fa-heart"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-comment"></i>
                </button>
                <button class="action-btn">
                  <i class="far fa-paper-plane"></i>
                </button>
              </div>
              <button class="action-btn bookmark">
                <i class="far fa-bookmark"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Original Form Content -->
        <div class="col-md-5 d-flex justify-content-center align-items-center">
          <div class="content-box" style="height: auto; width: 60rem">
            <!-- Notification Message -->
            <div class="notification-box mb-4">
              <div class="notification-content">
                <p class="timer-text mb-0">You still have {{ formattedTime }} to register!</p>
              </div>
            </div>
            <div class="clip">
              <h4 class="text-center fw-bold fs-6 text-primary shadow-8d">{{ Language.local_area }}</h4>
              <h3 class="text-center fs-4 text-light8 shadow-8 mt-3">{{ Language.username_input_text }}</h3>

              <div class="d-flex justify-content-center mt-4">
                <input v-model="props.inputs.username.value" class="form-control custom-input" style="width: 20rem" placeholder="e.g. Alex123" type="text" @focus="onFocus" @blur="onBlur" @keypress="handleKeyPress" />
              </div>
              <div v-if="isUsernameZero" class="d-flex justify-content-center">
                <span v-if="isInputFocused || props.inputs.username.value.trim().length > 5" class="text-light77 text-center mt-3">
                  <i>{{ Language.username_error_3 }}</i>
                </span>
              </div>

              <div v-else class="d-flex justify-content-center">
                <span v-if="isInputFocused || props.inputs.username.value.trim().length > 5" class="text-light77 text-center mt-3">
                  <i>{{ Language.username_error_2 }}</i>
                </span>
              </div>

              <p v-if="validationMessages.username" class="text-danger shadow-8 fs-6 mt-2">({{ validationMessages.username }})</p>
              <div class="button-group d-flex justify-content-center mt-3">
                <button type="button" class="btn next-btn" style="width: 20rem" @click="continueToNextSlide('yes')">
                  {{ Language.continue }}
                </button>
              </div>
              <Searching :location="location" :language="Language" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isUsernameZero" id="alert-message" style="display: none">
      {{ Language.username_error_3 }}
    </div>
    <div v-else id="alert-message" style="display: none">
      {{ Language.username_error_2 }}
    </div>
    <Steps :step="steps" />
  </div>
</template>

<style scoped>
/* Include styles from PageOne plus: */

.custom-input {
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  transition: all 0.3s ease;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.custom-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

.next-btn {
  background-color: #116d6e !important;
  color: #f6eca9 !important;
  font-size: 14px !important;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.next-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-light77 {
  color: #777;
}

.text-light8 {
  color: #282d4f !important;
  font-size: 24px !important;
}

.text-lighti {
  color: #a0204c !important;
  font-size: 16px !important;
  text-shadow: 3px 7px 8px rgba(134, 126, 126, 0.5);
}

.shadow-8 {
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
  font-size: 20px !important;
}

.shadow-8d {
  font-size: 15px !important;
}

/* Keep the rest of the styles from PageOne */
</style>
